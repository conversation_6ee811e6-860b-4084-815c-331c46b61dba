#!/usr/bin/env python3
"""
Script to find available ASL datasets on Kaggle
"""

import os
import json

def find_asl_datasets():
    """Search for ASL datasets on Kaggle"""
    
    print("🔍 Searching for ASL datasets on Kaggle...")
    print("=" * 50)
    
    try:
        import kaggle
        
        # Authenticate (should work now)
        kaggle.api.authenticate()
        print("✅ Authenticated with Kaggle API")
        
        # Search for ASL datasets
        search_terms = ["asl", "sign language", "american sign language"]
        
        all_datasets = []
        
        for term in search_terms:
            print(f"\n🔍 Searching for: '{term}'")
            try:
                datasets = kaggle.api.dataset_list(search=term, page=1)
                print(f"   Found {len(datasets)} datasets")
                
                for dataset in datasets[:5]:  # Show top 5 for each search
                    dataset_info = {
                        'ref': dataset.ref,
                        'title': dataset.title,
                        'size': dataset.size,
                        'downloadCount': dataset.downloadCount,
                        'voteCount': dataset.voteCount,
                        'usabilityRating': dataset.usabilityRating
                    }
                    all_datasets.append(dataset_info)
                    
                    print(f"   📊 {dataset.ref}")
                    print(f"      Title: {dataset.title}")
                    print(f"      Size: {dataset.size}")
                    print(f"      Downloads: {dataset.downloadCount}")
                    print(f"      Votes: {dataset.voteCount}")
                    print(f"      Rating: {dataset.usabilityRating}")
                    print()
                    
            except Exception as e:
                print(f"   ❌ Error searching for '{term}': {e}")
        
        # Look for specific ASL competition datasets
        print("\n🏆 Searching ASL competitions...")
        try:
            competitions = kaggle.api.competitions_list()
            asl_competitions = [comp for comp in competitions if 'asl' in comp.title.lower() or 'sign' in comp.title.lower()]
            
            if asl_competitions:
                print(f"   Found {len(asl_competitions)} ASL-related competitions:")
                for comp in asl_competitions[:3]:
                    print(f"   🏆 {comp.ref}")
                    print(f"      Title: {comp.title}")
                    print(f"      Description: {comp.description[:100]}...")
                    print()
            else:
                print("   No ASL competitions found")
                
        except Exception as e:
            print(f"   ❌ Error searching competitions: {e}")
        
        # Recommend best datasets
        print("\n🎯 RECOMMENDED ASL DATASETS:")
        print("=" * 40)
        
        # Sort by download count and rating
        sorted_datasets = sorted(all_datasets, key=lambda x: (x['downloadCount'], x['voteCount']), reverse=True)
        
        for i, dataset in enumerate(sorted_datasets[:5], 1):
            print(f"{i}. {dataset['ref']}")
            print(f"   Title: {dataset['title']}")
            print(f"   Size: {dataset['size']}")
            print(f"   Downloads: {dataset['downloadCount']}")
            print(f"   Rating: {dataset['usabilityRating']}")
            print()
        
        return sorted_datasets
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_dataset_access(dataset_ref):
    """Test if we can access a specific dataset"""
    try:
        import kaggle
        
        print(f"\n🔍 Testing access to: {dataset_ref}")
        
        # Try to get dataset metadata
        try:
            dataset_info = kaggle.api.dataset_view(dataset_ref)
            print(f"✅ Dataset accessible!")
            print(f"   Title: {dataset_info.title}")
            print(f"   Size: {dataset_info.size}")
            print(f"   Files: {len(dataset_info.files)} files")
            
            # Show file list
            print(f"   📁 Files:")
            for file_info in dataset_info.files[:10]:  # Show first 10 files
                print(f"      - {file_info.name} ({file_info.size})")
            
            return True
            
        except Exception as e:
            print(f"❌ Cannot access dataset: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing dataset: {e}")
        return False

if __name__ == "__main__":
    # Find datasets
    datasets = find_asl_datasets()
    
    # Test some common ASL dataset names
    common_datasets = [
        "danrasband/asl-signs",
        "grassknoted/asl-alphabet",
        "datamunge/sign-language-mnist",
        "ayuraj/asl-dataset",
        "lexset/synthetic-asl-alphabet"
    ]
    
    print("\n🧪 TESTING COMMON ASL DATASETS:")
    print("=" * 40)
    
    accessible_datasets = []
    for dataset_ref in common_datasets:
        if test_dataset_access(dataset_ref):
            accessible_datasets.append(dataset_ref)
    
    print(f"\n✅ ACCESSIBLE DATASETS:")
    print("=" * 30)
    for dataset in accessible_datasets:
        print(f"   📊 {dataset}")
    
    if not accessible_datasets:
        print("❌ No common ASL datasets are accessible")
        print("💡 Try using one of the recommended datasets from the search above")
