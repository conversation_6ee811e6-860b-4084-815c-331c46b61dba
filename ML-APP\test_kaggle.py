#!/usr/bin/env python3
"""
Test script to verify Kaggle API connection
"""

import os
import json

def test_kaggle_connection():
    """Test Kaggle API connection"""
    
    # Your credentials
    username = "imhari14"
    api_key = "fd250afcfc43cefbf8a92efb0e6526a7"
    
    print("🔍 Testing Kaggle API Connection")
    print("=" * 40)
    
    # Check home directory
    home_dir = os.path.expanduser('~')
    print(f"🏠 Home directory: {home_dir}")
    
    # Create .kaggle directory
    kaggle_dir = os.path.join(home_dir, '.kaggle')
    print(f"📁 Kaggle directory: {kaggle_dir}")
    
    try:
        os.makedirs(kaggle_dir, exist_ok=True)
        print(f"✅ Created/verified directory: {kaggle_dir}")
    except Exception as e:
        print(f"❌ Error creating directory: {e}")
        return False
    
    # Create kaggle.json
    kaggle_json_path = os.path.join(kaggle_dir, 'kaggle.json')
    kaggle_config = {
        "username": username,
        "key": api_key
    }
    
    try:
        with open(kaggle_json_path, 'w') as f:
            json.dump(kaggle_config, f, indent=2)
        print(f"✅ Created kaggle.json: {kaggle_json_path}")
        
        # Verify file exists and show size
        if os.path.exists(kaggle_json_path):
            size = os.path.getsize(kaggle_json_path)
            print(f"📄 File size: {size} bytes")
            
            # Show contents
            with open(kaggle_json_path, 'r') as f:
                content = f.read()
                print(f"📄 File contents:\n{content}")
        else:
            print(f"❌ File not found: {kaggle_json_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating kaggle.json: {e}")
        return False
    
    # Set environment variables
    os.environ['KAGGLE_USERNAME'] = username
    os.environ['KAGGLE_KEY'] = api_key
    print(f"✅ Set environment variables")
    
    # Test Kaggle import
    try:
        import kaggle
        print(f"✅ Kaggle package imported successfully")
        try:
            print(f"📦 Kaggle version: {kaggle.__version__}")
        except AttributeError:
            print("📦 Kaggle version: (version not available)")
    except ImportError as e:
        print(f"❌ Kaggle package not available: {e}")
        return False
    
    # Test authentication
    try:
        print("🔄 Testing authentication...")
        kaggle.api.authenticate()
        print("✅ Authentication successful!")
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False
    
    # Test API access
    try:
        print("🔄 Testing API access...")
        # Try different API calls to test access
        try:
            competitions = kaggle.api.competitions_list()[:1]  # Get first competition
            print(f"✅ API access successful! Found {len(competitions)} competition(s)")

            if competitions:
                comp = competitions[0]
                print(f"📊 Sample competition: {comp.title}")
        except Exception as comp_error:
            print(f"⚠️ Competitions API failed: {comp_error}")
            # Try datasets instead
            try:
                datasets = kaggle.api.dataset_list(search='', page=1)[:1]
                print(f"✅ Dataset API access successful! Found {len(datasets)} dataset(s)")
            except Exception as dataset_error:
                print(f"❌ Dataset API also failed: {dataset_error}")
                return False

        return True

    except Exception as e:
        print(f"❌ API access failed: {e}")
        return False

if __name__ == "__main__":
    success = test_kaggle_connection()
    
    if success:
        print("\n🎉 SUCCESS! Kaggle API is working correctly!")
        print("You can now use the Streamlit app with confidence.")
    else:
        print("\n❌ FAILED! There are still issues with the Kaggle API.")
        print("Please check the error messages above.")
