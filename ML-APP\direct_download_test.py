#!/usr/bin/env python3
"""
Direct download test for ASL datasets
"""

import kaggle
import os
import tempfile
import shutil

def test_direct_download(dataset_ref):
    """Test direct download without checking file list first"""
    try:
        print(f"🔍 Testing direct download: {dataset_ref}")
        
        # Create a temporary directory for testing
        test_dir = f"./test_{dataset_ref.replace('/', '_')}"
        
        try:
            # Try direct download
            kaggle.api.dataset_download_files(dataset_ref, path=test_dir, unzip=True)
            
            # Check what was downloaded
            if os.path.exists(test_dir):
                downloaded_files = []
                total_size = 0
                
                for root, dirs, files in os.walk(test_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        downloaded_files.append(file_path)
                        total_size += os.path.getsize(file_path)
                
                print(f"✅ SUCCESS! Downloaded {len(downloaded_files)} files ({total_size:,} bytes)")
                
                # Show some files
                for file_path in downloaded_files[:5]:
                    rel_path = os.path.relpath(file_path, test_dir)
                    size = os.path.getsize(file_path)
                    print(f"   📄 {rel_path} ({size:,} bytes)")
                
                if len(downloaded_files) > 5:
                    print(f"   ... and {len(downloaded_files) - 5} more files")
                
                # Clean up
                shutil.rmtree(test_dir)
                print(f"   🧹 Cleaned up test files")
                
                return True, len(downloaded_files), total_size
            else:
                print(f"❌ Download directory not created")
                return False, 0, 0
                
        except Exception as download_error:
            error_msg = str(download_error).lower()
            if "403" in error_msg or "forbidden" in error_msg:
                print(f"❌ Access denied")
            elif "404" in error_msg or "not found" in error_msg:
                print(f"❌ Dataset not found")
            else:
                print(f"❌ Download error: {download_error}")
            
            # Clean up if directory was created
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
            
            return False, 0, 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0, 0

def main():
    print("🔍 Direct Download Test for ASL Datasets")
    print("=" * 50)
    
    # Test smaller, more likely public datasets first
    datasets_to_test = [
        "datamunge/sign-language-mnist",  # MNIST-style, likely small and public
        "grassknoted/asl-alphabet",      # Alphabet dataset, likely public
        "ayuraj/asl-dataset",            # Generic ASL dataset
        "ardamavi/sign-language-digits-dataset",  # Digits only, smaller
        "muhammadkhalid/sign-language-for-numbers",  # Numbers only
        "lexset/synthetic-asl-alphabet",  # Synthetic data, likely public
        "ash2703/handsignimages",        # Hand sign images
        "kapillondhe/american-sign-language",  # ASL dataset
    ]
    
    successful_downloads = []
    
    for dataset in datasets_to_test:
        success, file_count, total_size = test_direct_download(dataset)
        if success:
            successful_downloads.append((dataset, file_count, total_size))
        print()
    
    print("🎯 RESULTS")
    print("=" * 20)
    
    if successful_downloads:
        print("✅ SUCCESSFULLY DOWNLOADABLE DATASETS:")
        
        # Sort by size (smaller first for easier testing)
        successful_downloads.sort(key=lambda x: x[2])
        
        for i, (dataset, file_count, total_size) in enumerate(successful_downloads, 1):
            size_mb = total_size / (1024 * 1024)
            print(f"   {i}. {dataset}")
            print(f"      📁 {file_count} files")
            print(f"      💾 {size_mb:.1f} MB")
            print()
        
        # Recommend the best one (smallest for testing, but with good file count)
        best_dataset = successful_downloads[0][0]
        best_size = successful_downloads[0][2] / (1024 * 1024)
        
        print(f"💡 RECOMMENDED FOR STREAMLIT APP:")
        print(f"   📊 Dataset: {best_dataset}")
        print(f"   📁 Files: {successful_downloads[0][1]}")
        print(f"   💾 Size: {best_size:.1f} MB")
        
        return best_dataset
        
    else:
        print("❌ NO DATASETS COULD BE DOWNLOADED")
        print("\n💡 POSSIBLE ISSUES:")
        print("   1. Your Kaggle account may need verification")
        print("   2. You may need to accept terms for specific datasets")
        print("   3. Some datasets may require competition participation")
        print("\n🔧 SOLUTIONS:")
        print("   1. Verify your Kaggle account (phone number)")
        print("   2. Join some Kaggle competitions")
        print("   3. Try a different Kaggle account")
        print("   4. Use a local dataset instead")
        
        return None

if __name__ == "__main__":
    working_dataset = main()
    
    if working_dataset:
        print(f"\n🎉 SUCCESS!")
        print(f"Update your Streamlit app to use: {working_dataset}")
        print(f"\nTo update the app:")
        print(f"1. Go to Data Loading page")
        print(f"2. Change dataset name to: {working_dataset}")
        print(f"3. Click Download Dataset")
    else:
        print(f"\n❌ No working dataset found")
        print(f"Consider using a local dataset or synthetic data")
