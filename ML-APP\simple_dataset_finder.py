#!/usr/bin/env python3
"""
Simple script to find and test ASL datasets
"""

import kaggle

def test_dataset_download(dataset_ref):
    """Test if we can download a dataset"""
    try:
        print(f"🔍 Testing: {dataset_ref}")
        
        # Try to list dataset files (this will fail if we don't have access)
        files = kaggle.api.dataset_list_files(dataset_ref)
        print(f"✅ Accessible! Found {len(files)} files:")
        
        # Show first few files
        for file_info in files[:5]:
            print(f"   📄 {file_info.name}")
        
        return True
        
    except Exception as e:
        error_msg = str(e).lower()
        if "403" in error_msg or "forbidden" in error_msg:
            print(f"❌ Access denied: {dataset_ref}")
        elif "404" in error_msg or "not found" in error_msg:
            print(f"❌ Not found: {dataset_ref}")
        else:
            print(f"❌ Error: {dataset_ref} - {e}")
        return False

def main():
    print("🔍 Testing ASL Datasets on Kaggle")
    print("=" * 40)
    
    # Test common ASL datasets
    datasets_to_test = [
        "danrasband/asl-signs",
        "grassknoted/asl-alphabet", 
        "datamunge/sign-language-mnist",
        "ayuraj/asl-dataset",
        "lexset/synthetic-asl-alphabet",
        "kuzivakwashe/sign-language-mnist",
        "muhammadkhalid/sign-language-for-numbers",
        "ardamavi/sign-language-digits-dataset",
        "debashishsau/aslamerican-sign-language-aplhabet-dataset",
        "kapillondhe/american-sign-language",
        "risangbaskoro/wlasl-processed",
        "sttaseen/asl-alphabet-image-dataset",
        "prathusha/american-sign-language-09az",
        "vaishnaviasonawane/american-sign-language-dataset",
        "ash2703/handsignimages"
    ]
    
    accessible_datasets = []
    
    for dataset in datasets_to_test:
        if test_dataset_download(dataset):
            accessible_datasets.append(dataset)
        print()
    
    print("🎯 SUMMARY")
    print("=" * 20)
    
    if accessible_datasets:
        print("✅ ACCESSIBLE DATASETS:")
        for i, dataset in enumerate(accessible_datasets, 1):
            print(f"   {i}. {dataset}")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Use: {accessible_datasets[0]}")
        
    else:
        print("❌ No datasets are accessible with your current permissions")
        print("💡 Possible solutions:")
        print("   1. Check if your Kaggle account is verified")
        print("   2. Try joining some competitions to increase access")
        print("   3. Look for public datasets that don't require special permissions")

if __name__ == "__main__":
    main()
