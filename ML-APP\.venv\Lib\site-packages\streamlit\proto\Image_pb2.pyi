"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Image(google.protobuf.message.Message):
    """An image which can be displayed on the screen."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    CAPTION_FIELD_NUMBER: builtins.int
    MARKUP_FIELD_NUMBER: builtins.int
    url: builtins.str
    caption: builtins.str
    markup: builtins.str
    """DEPRECATED: markup is not used anymore.
    SVGs are added as data uris in the url field.
    """
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        caption: builtins.str = ...,
        markup: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["caption", b"caption", "markup", b"markup", "url", b"url"]) -> None: ...

global___Image = Image

@typing.final
class ImageList(google.protobuf.message.Message):
    """A set of images."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMGS_FIELD_NUMBER: builtins.int
    WIDTH_FIELD_NUMBER: builtins.int
    width: builtins.int
    """@see WidthBehavior on the backend
    @see WidthBehavior on the frontend
    The width of each image.
    >0 sets the image width explicitly
    -1 means use the image width
    -2 means use the column width (deprecated)
    -3 means use the smaller of image width & column width (deprecated)
    -4 means use the smaller of image width & container width
    -5 means use the larger of image width & container width
    """
    @property
    def imgs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Image]: ...
    def __init__(
        self,
        *,
        imgs: collections.abc.Iterable[global___Image] | None = ...,
        width: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["imgs", b"imgs", "width", b"width"]) -> None: ...

global___ImageList = ImageList
