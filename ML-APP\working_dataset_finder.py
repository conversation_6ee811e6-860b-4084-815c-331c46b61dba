#!/usr/bin/env python3
"""
Find working ASL datasets that we can actually download
"""

import kaggle
import os
import tempfile
import shutil

def test_dataset_download(dataset_ref):
    """Test if we can actually download a dataset"""
    try:
        print(f"🔍 Testing download: {dataset_ref}")
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # Try to download just the metadata/file list
                files = kaggle.api.dataset_list_files(dataset_ref)
                
                # Convert to list to check if it's accessible
                file_list = list(files)
                print(f"✅ Accessible! Found {len(file_list)} files")
                
                # Show first few files
                for i, file_info in enumerate(file_list[:3]):
                    print(f"   📄 {file_info.name}")
                
                # Try a small download test (just list, don't actually download large files)
                print(f"   🔍 Testing download capability...")
                
                return True, len(file_list)
                
            except Exception as download_error:
                error_msg = str(download_error).lower()
                if "403" in error_msg or "forbidden" in error_msg:
                    print(f"❌ Access denied")
                elif "404" in error_msg or "not found" in error_msg:
                    print(f"❌ Dataset not found")
                else:
                    print(f"❌ Download error: {download_error}")
                return False, 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def main():
    print("🔍 Finding Working ASL Datasets")
    print("=" * 40)
    
    # Test datasets that are more likely to be public
    datasets_to_test = [
        "grassknoted/asl-alphabet",
        "datamunge/sign-language-mnist", 
        "ayuraj/asl-dataset",
        "lexset/synthetic-asl-alphabet",
        "muhammadkhalid/sign-language-for-numbers",
        "ardamavi/sign-language-digits-dataset",
        "debashishsau/aslamerican-sign-language-aplhabet-dataset",
        "kapillondhe/american-sign-language",
        "risangbaskoro/wlasl-processed",
        "ash2703/handsignimages"
    ]
    
    working_datasets = []
    
    for dataset in datasets_to_test:
        success, file_count = test_dataset_download(dataset)
        if success:
            working_datasets.append((dataset, file_count))
        print()
    
    print("🎯 WORKING DATASETS")
    print("=" * 30)
    
    if working_datasets:
        print("✅ FOUND WORKING DATASETS:")
        for i, (dataset, file_count) in enumerate(working_datasets, 1):
            print(f"   {i}. {dataset} ({file_count} files)")
        
        # Recommend the best one
        best_dataset = working_datasets[0][0]
        print(f"\n💡 RECOMMENDED DATASET:")
        print(f"   📊 {best_dataset}")
        print(f"   📁 {working_datasets[0][1]} files")
        
        # Test actual download of the recommended dataset
        print(f"\n🧪 TESTING ACTUAL DOWNLOAD OF: {best_dataset}")
        try:
            test_dir = "./test_download"
            os.makedirs(test_dir, exist_ok=True)
            
            print(f"   📥 Downloading to: {test_dir}")
            kaggle.api.dataset_download_files(best_dataset, path=test_dir, unzip=True)
            
            # Check what was downloaded
            downloaded_files = []
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    downloaded_files.append(os.path.join(root, file))
            
            print(f"   ✅ Successfully downloaded {len(downloaded_files)} files!")
            
            # Show some downloaded files
            for file_path in downloaded_files[:5]:
                rel_path = os.path.relpath(file_path, test_dir)
                size = os.path.getsize(file_path)
                print(f"      📄 {rel_path} ({size} bytes)")
            
            # Clean up test download
            shutil.rmtree(test_dir)
            print(f"   🧹 Cleaned up test download")
            
            return best_dataset
            
        except Exception as e:
            print(f"   ❌ Download test failed: {e}")
            return None
        
    else:
        print("❌ No working datasets found")
        print("\n💡 ALTERNATIVE SOLUTIONS:")
        print("   1. Use a local dataset")
        print("   2. Create synthetic ASL data")
        print("   3. Try different Kaggle account with more permissions")
        print("   4. Look for datasets outside Kaggle")
        return None

if __name__ == "__main__":
    working_dataset = main()
    
    if working_dataset:
        print(f"\n🎉 SUCCESS!")
        print(f"   Use this dataset in your Streamlit app: {working_dataset}")
    else:
        print(f"\n❌ No suitable dataset found")
