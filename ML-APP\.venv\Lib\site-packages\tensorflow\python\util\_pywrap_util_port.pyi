# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

def GpuSupportsHalfMatMulAndConv() -> bool: ...
def IsAArch32Available() -> bool: ...
def IsAArch64Available() -> bool: ...
def IsBuiltWithNvcc() -> bool: ...
def IsBuiltWithROCm() -> bool: ...
def IsBuiltWithXLA() -> bool: ...
def IsGoogleCudaEnabled() -> bool: ...
def IsMklEnabled() -> bool: ...
def IsPowerPCAvailable() -> bool: ...
def IsSystemZAvailable() -> bool: ...
def IsX86Available() -> bool: ...
