# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/python/tpu/tensor_tracer.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import graph_pb2 as tensorflow_dot_core_dot_framework_dot_graph__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)tensorflow/python/tpu/tensor_tracer.proto\x12\ntensorflow\x1a%tensorflow/core/framework/graph.proto\"\xb4\t\n\x12TensorTracerReport\x12\x41\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x31.tensorflow.TensorTracerReport.TensorTracerConfig\x12&\n\x08graphdef\x18\x02 \x01(\x0b\x32\x14.tensorflow.GraphDef\x12@\n\ttensordef\x18\x03 \x03(\x0b\x32-.tensorflow.TensorTracerReport.TensordefEntry\x12\x13\n\x0b\x66ingerprint\x18\x04 \x01(\t\x12\x1e\n\x16\x63oncrete_function_name\x18\x05 \x01(\t\x12\x1c\n\x14last_common_frame_no\x18\x06 \x01(\x05\x12\x0f\n\x07outputs\x18\x07 \x03(\t\x12\x42\n\rtracing_stats\x18\x08 \x01(\x0b\x32+.tensorflow.TensorTracerReport.TracingStats\x1a`\n\x0eTensordefEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12=\n\x05value\x18\x02 \x01(\x0b\x32..tensorflow.TensorTracerReport.TracedTensorDef:\x02\x38\x01\x1a\xc8\x01\n\x12TensorTracerConfig\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65vice\x18\x02 \x01(\t\x12\x12\n\ntrace_mode\x18\x03 \x01(\t\x12\x11\n\tnum_cores\x18\x04 \x01(\x05\x12\x11\n\tnum_hosts\x18\x05 \x01(\x05\x12\x0f\n\x07submode\x18\x06 \x01(\t\x12\x1a\n\x12num_cores_per_host\x18\x07 \x01(\x05\x12\x16\n\x0eincluded_cores\x18\x08 \x03(\x05\x12\x12\n\nsignatures\x18\t \x03(\t\x1a\xef\x01\n\x0cTracingStats\x12\x15\n\rtotal_tensors\x18\x01 \x01(\x05\x12\x16\n\x0etraced_tensors\x18\x02 \x01(\x05\x12_\n\x13traced_tensor_types\x18\x03 \x03(\x0b\x32\x42.tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry\x12\x15\n\radded_tensors\x18\x04 \x01(\x05\x1a\x38\n\x16TracedTensorTypesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\xa9\x02\n\x0fTracedTensorDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x61\x63he_index\x18\x02 \x01(\x05\x12\x18\n\x10trace_point_name\x18\x03 \x01(\t\x12\x11\n\tis_traced\x18\x04 \x01(\x08\x12\x13\n\x0b\x65xplanation\x18\x05 \x01(\t\x12K\n\rop_stack_info\x18\x06 \x01(\x0b\x32\x34.tensorflow.TensorTracerReport.TracedTensorDef.Stack\x1a\x64\n\x05Stack\x12\x16\n\x0estack_fn_names\x18\x01 \x03(\t\x12\x13\n\x0bstack_lines\x18\x02 \x03(\t\x12\x17\n\x0fstack_filenames\x18\x03 \x03(\t\x12\x15\n\rstack_linenos\x18\x04 \x03(\x05\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.python.tpu.tensor_tracer_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _TENSORTRACERREPORT_TENSORDEFENTRY._options = None
  _TENSORTRACERREPORT_TENSORDEFENTRY._serialized_options = b'8\001'
  _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY._options = None
  _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY._serialized_options = b'8\001'
  _TENSORTRACERREPORT._serialized_start=97
  _TENSORTRACERREPORT._serialized_end=1301
  _TENSORTRACERREPORT_TENSORDEFENTRY._serialized_start=460
  _TENSORTRACERREPORT_TENSORDEFENTRY._serialized_end=556
  _TENSORTRACERREPORT_TENSORTRACERCONFIG._serialized_start=559
  _TENSORTRACERREPORT_TENSORTRACERCONFIG._serialized_end=759
  _TENSORTRACERREPORT_TRACINGSTATS._serialized_start=762
  _TENSORTRACERREPORT_TRACINGSTATS._serialized_end=1001
  _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY._serialized_start=945
  _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY._serialized_end=1001
  _TENSORTRACERREPORT_TRACEDTENSORDEF._serialized_start=1004
  _TENSORTRACERREPORT_TRACEDTENSORDEF._serialized_end=1301
  _TENSORTRACERREPORT_TRACEDTENSORDEF_STACK._serialized_start=1201
  _TENSORTRACERREPORT_TRACEDTENSORDEF_STACK._serialized_end=1301
# @@protoc_insertion_point(module_scope)
