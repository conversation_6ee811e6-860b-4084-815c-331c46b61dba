"""
ASL Signs Recognition Streamlit Application
Converted from <PERSON><PERSON><PERSON> notebook with Kaggle API integration
Author: Based on <PERSON><PERSON><PERSON><PERSON>'s notebook
"""

import streamlit as st
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tensorflow as tf
from tensorflow.keras import layers
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report
import os
import json
import time
import gc
import random
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="ASL Signs Recognition",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'model_trained' not in st.session_state:
    st.session_state.model_trained = False
if 'kaggle_authenticated' not in st.session_state:
    st.session_state.kaggle_authenticated = False
if 'features' not in st.session_state:
    st.session_state.features = None
if 'labels' not in st.session_state:
    st.session_state.labels = None
if 'model' not in st.session_state:
    st.session_state.model = None
if 'history' not in st.session_state:
    st.session_state.history = None
if 's2p_map' not in st.session_state:
    st.session_state.s2p_map = None
if 'p2s_map' not in st.session_state:
    st.session_state.p2s_map = None

# Configuration constants
SEED = 42
ROWS_PER_FRAME = 543
LANDMARK = [0, 9, 11, 13, 14, 17, 117, 118, 119, 199, 346, 347, 348] + list(range(468, 543))
DROP_Z = False
N_DATA = 2 if DROP_Z else 3

def seed_all(seed=SEED):
    """Set all random seeds for reproducibility"""
    os.environ["PYTHONHASHSEED"] = str(seed)
    random.seed(seed)
    np.random.seed(seed)
    tf.random.set_seed(seed)

seed_all()

# Main application
def main():
    # Header
    st.markdown('<h1 class="main-header">🤟 ASL Signs Recognition</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Advanced Machine Learning with PySpark Integration</p>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("🚀 Navigation")
    page = st.sidebar.selectbox(
        "Choose a section:",
        [
            "🏠 Home",
            "🔑 Kaggle Setup", 
            "📊 Data Loading",
            "🔧 Data Processing",
            "🤖 Model Training",
            "🎯 Random Testing",
            "📈 Metrics Dashboard",
            "⚙️ Settings"
        ]
    )
    
    # Page routing
    if page == "🏠 Home":
        show_home_page()
    elif page == "🔑 Kaggle Setup":
        show_kaggle_setup()
    elif page == "📊 Data Loading":
        show_data_loading()
    elif page == "🔧 Data Processing":
        show_data_processing()
    elif page == "🤖 Model Training":
        show_model_training()
    elif page == "🎯 Random Testing":
        show_random_testing()
    elif page == "📈 Metrics Dashboard":
        show_metrics_dashboard()
    elif page == "⚙️ Settings":
        show_settings()

def show_home_page():
    """Display the home page with project overview"""
    st.markdown('<h2 class="sub-header">🎯 Project Overview</h2>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3>📊 Dataset</h3>
            <p>ASL Signs Recognition</p>
            <p><strong>250 Classes</strong></p>
            <p>~95,000 Samples</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3>🤖 Model</h3>
            <p>Advanced 1D CNN</p>
            <p><strong>PySpark Integration</strong></p>
            <p>Feature Engineering</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3>🎯 Performance</h3>
            <p>Target: 48.8% Accuracy</p>
            <p><strong>250-Class Problem</strong></p>
            <p>Real-time Inference</p>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Features overview
    st.markdown('<h3 class="sub-header">✨ Key Features</h3>', unsafe_allow_html=True)
    
    features = [
        "🔑 **Kaggle API Integration** - Automatic dataset download and authentication",
        "📊 **Advanced Data Processing** - PySpark-powered feature engineering",
        "🤖 **1D CNN Architecture** - Optimized for temporal ASL data",
        "🎯 **Random Test Selection** - Interactive prediction testing",
        "📈 **Comprehensive Metrics** - Detailed performance visualization",
        "⚡ **Real-time Processing** - Fast inference and updates",
        "💾 **Model Persistence** - Save and load trained models",
        "🎨 **Interactive Dashboard** - Beautiful visualizations and insights"
    ]
    
    for feature in features:
        st.markdown(feature)
    
    st.markdown("---")
    
    # Getting started
    st.markdown('<h3 class="sub-header">🚀 Getting Started</h3>', unsafe_allow_html=True)
    
    st.markdown("""
    1. **🔑 Kaggle Setup**: Configure your Kaggle API credentials
    2. **📊 Data Loading**: Download and load the ASL dataset
    3. **🔧 Data Processing**: Apply PySpark feature engineering
    4. **🤖 Model Training**: Train the 1D CNN model
    5. **🎯 Random Testing**: Test predictions on random samples
    6. **📈 Metrics Dashboard**: Analyze model performance
    """)
    
    # Status indicators
    st.markdown('<h3 class="sub-header">📊 Current Status</h3>', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        status = "✅" if st.session_state.kaggle_authenticated else "❌"
        st.metric("Kaggle Auth", status)
    
    with col2:
        status = "✅" if st.session_state.data_loaded else "❌"
        st.metric("Data Loaded", status)
    
    with col3:
        status = "✅" if st.session_state.model_trained else "❌"
        st.metric("Model Trained", status)
    
    with col4:
        samples = len(st.session_state.features) if st.session_state.features is not None else 0
        st.metric("Samples", f"{samples:,}")

def show_kaggle_setup():
    """Display Kaggle API setup page"""
    st.markdown('<h2 class="sub-header">🔑 Kaggle API Setup</h2>', unsafe_allow_html=True)

    st.markdown("""
    Enter your Kaggle API credentials to download the ASL dataset.
    """)

    # Instructions
    with st.expander("📋 How to get your credentials", expanded=False):
        st.markdown("""
        1. **Go to Kaggle**: Visit [kaggle.com](https://www.kaggle.com) and sign in
        2. **Account Settings**: Click on your profile → Account
        3. **API Section**: Scroll down to the API section
        4. **Create Token**: Click "Create New API Token"
        5. **Download File**: This downloads `kaggle.json` with your credentials
        6. **Copy Values**: Open the file and copy the username and key values
        """)

    # Credential input form
    st.markdown("### 🔐 Enter Your Kaggle Credentials")

    # Create a form for better UX
    with st.form("kaggle_credentials"):
        col1, col2 = st.columns(2)

        with col1:
            username = st.text_input(
                "Kaggle Username",
                value=st.session_state.get('kaggle_username', ''),
                placeholder="e.g., maladhikamohan",
                help="Your Kaggle username (from kaggle.json)"
            )

        with col2:
            api_key = st.text_input(
                "API Key",
                value=st.session_state.get('kaggle_api_key', ''),
                type="password",
                placeholder="Enter your API key here",
                help="Your Kaggle API key (from kaggle.json)"
            )

        # Form submit button
        submitted = st.form_submit_button("🔍 Test & Save Credentials", type="primary")

        if submitted:
            if username and api_key:
                with st.spinner("Testing Kaggle connection..."):
                    success = test_kaggle_connection(username, api_key)
                    if success:
                        st.session_state.kaggle_authenticated = True
                        st.session_state.kaggle_username = username
                        st.session_state.kaggle_api_key = api_key
                        st.success("✅ Connection successful! Credentials saved.")
                        st.rerun()
                    else:
                        st.error("❌ Connection failed. Please check your credentials.")
            else:
                st.warning("⚠️ Please enter both username and API key.")

    # Quick package check
    st.markdown("### 🔧 System Check")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔍 Check Kaggle Package", type="secondary"):
            try:
                import kaggle
                st.success(f"✅ Kaggle package installed: v{kaggle.__version__}")

                # Check if kaggle config exists
                kaggle_dir = os.path.expanduser('~/.kaggle')
                if os.path.exists(kaggle_dir):
                    st.info(f"📁 Kaggle config directory exists: {kaggle_dir}")
                else:
                    st.info("📁 No existing Kaggle config directory")

            except ImportError:
                st.error("❌ Kaggle package not installed!")
                st.code("pip install kaggle", language="bash")
            except Exception as e:
                st.error(f"❌ Error checking Kaggle: {e}")

    with col2:
        if st.button("🌐 Test Internet", type="secondary"):
            try:
                import requests
                response = requests.get("https://www.kaggle.com", timeout=10)
                if response.status_code == 200:
                    st.success("✅ Internet connection OK")
                    st.success("✅ Kaggle.com is accessible")
                else:
                    st.warning(f"⚠️ Kaggle.com returned status: {response.status_code}")
            except Exception as e:
                st.error(f"❌ Internet/Kaggle connection issue: {e}")

    # Connection status
    st.markdown("### 📊 Connection Status")

    if st.session_state.kaggle_authenticated:
        st.markdown("""
        <div class="success-box">
            <strong>✅ Kaggle API Connected</strong><br>
            Username: <code>{}</code><br>
            Status: Ready to download datasets
        </div>
        """.format(st.session_state.get('kaggle_username', 'Unknown')), unsafe_allow_html=True)

        # Clear credentials button
        if st.button("🗑️ Clear Credentials", type="secondary"):
            st.session_state.kaggle_authenticated = False
            st.session_state.kaggle_username = ''
            st.session_state.kaggle_api_key = ''
            st.success("✅ Credentials cleared!")
            st.rerun()
    else:
        st.markdown("""
        <div class="warning-box">
            <strong>⚠️ Kaggle API Not Connected</strong><br>
            Please enter your credentials above to proceed.
        </div>
        """, unsafe_allow_html=True)

    # Quick example with your actual credentials
    if not st.session_state.kaggle_authenticated:
        st.markdown("### 💡 Your Credentials")
        st.info("Based on your kaggle.json file, enter these values:")

        col1, col2 = st.columns(2)
        with col1:
            st.code("Username: imhari14", language="text")
        with col2:
            st.code("API Key: fd250afcfc43cefbf8a92efb0e6526a7", language="text")

        st.markdown("**Copy and paste these values into the form above** ⬆️")

def test_kaggle_connection(username, api_key):
    """Test Kaggle API connection with detailed debugging"""
    try:
        # First, try to import kaggle
        try:
            import kaggle
            st.info("✅ Kaggle package imported successfully")
        except ImportError:
            st.error("❌ Kaggle package not installed. Please run: pip install kaggle")
            return False

        # Clean and validate credentials
        username = username.strip()
        api_key = api_key.strip()

        if not username or not api_key:
            st.error("❌ Username or API key is empty")
            return False

        st.info(f"🔍 Testing connection for user: {username}")
        st.info(f"🔑 API key length: {len(api_key)} characters")

        # Set up Kaggle API credentials in environment
        os.environ['KAGGLE_USERNAME'] = username
        os.environ['KAGGLE_KEY'] = api_key

        # Also create the kaggle.json file temporarily to ensure compatibility
        kaggle_dir = os.path.expanduser('~/.kaggle')
        kaggle_json_path = os.path.join(kaggle_dir, 'kaggle.json')

        # Create .kaggle directory if it doesn't exist
        os.makedirs(kaggle_dir, exist_ok=True)
        st.info(f"📁 Created/verified Kaggle config directory: {kaggle_dir}")

        # Create kaggle.json file
        kaggle_config = {
            "username": username,
            "key": api_key
        }

        try:
            with open(kaggle_json_path, 'w') as f:
                json.dump(kaggle_config, f)

            # Set proper permissions (important for security)
            if os.name != 'nt':  # Not Windows
                os.chmod(kaggle_json_path, 0o600)

            st.info(f"✅ Created kaggle.json at: {kaggle_json_path}")

        except Exception as file_error:
            st.warning(f"⚠️ Could not create kaggle.json: {file_error}")
            st.info("Continuing with environment variables only...")

        # Force reload the kaggle API to pick up new credentials
        import importlib
        importlib.reload(kaggle)

        # Test connection by authenticating
        st.info("🔄 Attempting to authenticate...")
        try:
            kaggle.api.authenticate()
            st.info("✅ Authentication successful!")
        except Exception as auth_error:
            st.error(f"❌ Authentication failed: {auth_error}")

            # Try to fix common authentication issues
            if "kaggle.json" in str(auth_error):
                st.info("🔧 Trying alternative authentication method...")

                # Clear any cached authentication
                if hasattr(kaggle.api, '_credentials'):
                    delattr(kaggle.api, '_credentials')

                # Try again
                try:
                    kaggle.api.authenticate()
                    st.info("✅ Authentication successful on retry!")
                except Exception as retry_error:
                    st.error(f"❌ Authentication still failed: {retry_error}")
                    return False
            else:
                return False

        # Try to access a simple API endpoint
        try:
            st.info("🔄 Testing API access...")
            # Test with competitions list (simpler than datasets)
            competitions = kaggle.api.competitions_list(page=1, page_size=1)
            st.success(f"✅ Successfully connected to Kaggle API! Found {len(competitions)} competition(s)")
            return True

        except Exception as api_error:
            st.error(f"❌ API Access Error: {str(api_error)}")

            # More specific error handling
            error_str = str(api_error).lower()
            if "401" in error_str or "unauthorized" in error_str:
                st.error("🔑 **Authentication Failed**")
                st.error("Please verify:")
                st.error("- Username is correct (case-sensitive)")
                st.error("- API key is complete and not truncated")
                st.error("- Your Kaggle account is verified")
            elif "403" in error_str or "forbidden" in error_str:
                st.error("🚫 **Access Forbidden**")
                st.error("Please verify:")
                st.error("- Your Kaggle account is active")
                st.error("- You have accepted Kaggle's terms")
                st.error("- Your account has API access enabled")
            elif "404" in error_str:
                st.error("🔍 **Resource Not Found**")
                st.error("This might be a temporary Kaggle API issue")
            else:
                st.error(f"🔧 **Unknown API Error**: {api_error}")

            return False

    except Exception as e:
        st.error(f"❌ **Connection Error**: {str(e)}")

        error_msg = str(e).lower()
        if "kaggle.json" in error_msg:
            st.error("🔧 **Kaggle Config Issue**")
            st.error("The API is looking for kaggle.json file")
            st.error("We're creating it automatically now...")
        elif "ssl" in error_msg or "certificate" in error_msg:
            st.error("🔒 **SSL/Certificate Issue**")
            st.error("Try: pip install --upgrade certifi")
        elif "connection" in error_msg or "network" in error_msg:
            st.error("🌐 **Network Issue**")
            st.error("Please check your internet connection")
        elif "timeout" in error_msg:
            st.error("⏱️ **Timeout Error**")
            st.error("Kaggle API might be slow, try again")
        else:
            st.error(f"🔧 **Unexpected Error**: {e}")

        return False

def download_kaggle_dataset(dataset_name, download_path):
    """Download dataset from Kaggle"""
    try:
        import kaggle

        # Authenticate using stored credentials
        if not st.session_state.kaggle_authenticated:
            st.error("❌ Please authenticate with Kaggle first!")
            return False

        # Set up credentials
        username = st.session_state.kaggle_username
        api_key = st.session_state.kaggle_api_key

        os.environ['KAGGLE_USERNAME'] = username
        os.environ['KAGGLE_KEY'] = api_key

        # Ensure kaggle.json exists
        kaggle_dir = os.path.expanduser('~/.kaggle')
        kaggle_json_path = os.path.join(kaggle_dir, 'kaggle.json')

        os.makedirs(kaggle_dir, exist_ok=True)

        kaggle_config = {
            "username": username,
            "key": api_key
        }

        with open(kaggle_json_path, 'w') as f:
            json.dump(kaggle_config, f)

        # Set proper permissions
        if os.name != 'nt':  # Not Windows
            os.chmod(kaggle_json_path, 0o600)

        # Force reload kaggle API
        import importlib
        importlib.reload(kaggle)

        kaggle.api.authenticate()

        # Create download directory
        os.makedirs(download_path, exist_ok=True)
        st.info(f"📁 Created directory: {download_path}")

        # Download dataset with progress
        st.info(f"📥 Downloading dataset: {dataset_name}")
        kaggle.api.dataset_download_files(
            dataset_name,
            path=download_path,
            unzip=True
        )

        # Verify download
        if os.path.exists(os.path.join(download_path, "train.csv")):
            st.success(f"✅ Dataset downloaded successfully to {download_path}")
            return True
        else:
            st.error("❌ Download completed but expected files not found!")
            return False

    except Exception as e:
        error_msg = str(e)
        if "404" in error_msg:
            st.error(f"❌ Dataset not found: {dataset_name}")
        elif "403" in error_msg:
            st.error("❌ Access denied. Please check dataset permissions.")
        else:
            st.error(f"❌ Download error: {e}")
        return False

def show_data_loading():
    """Display data loading page"""
    st.markdown('<h2 class="sub-header">📊 Data Loading</h2>', unsafe_allow_html=True)

    if not st.session_state.kaggle_authenticated:
        st.warning("⚠️ Please configure Kaggle API credentials first.")
        return

    # Dataset configuration
    st.markdown("### 🎯 Dataset Configuration")

    col1, col2 = st.columns(2)

    with col1:
        dataset_name = st.text_input(
            "Dataset Name",
            value="danrasband/asl-signs",
            help="Kaggle dataset identifier"
        )

    with col2:
        data_percentage = st.slider(
            "Data Percentage",
            min_value=10,
            max_value=100,
            value=80,
            step=10,
            help="Percentage of dataset to load"
        )

    # Download section
    st.markdown("### 📥 Download Dataset")

    download_path = st.text_input(
        "Download Path",
        value="./data/asl-signs",
        help="Local path to download dataset"
    )

    col1, col2 = st.columns(2)

    with col1:
        if st.button("📥 Download Dataset", type="primary"):
            with st.spinner("Downloading dataset..."):
                progress_bar = st.progress(0)
                status_text = st.empty()

                status_text.text("Connecting to Kaggle...")
                progress_bar.progress(20)

                success = download_kaggle_dataset(dataset_name, download_path)

                if success:
                    progress_bar.progress(100)
                    status_text.text("Download complete!")
                    st.success("✅ Dataset downloaded successfully!")
                else:
                    st.error("❌ Download failed!")

    with col2:
        if st.button("🔄 Load Data"):
            if os.path.exists(download_path):
                with st.spinner("Loading data..."):
                    success = load_asl_data(download_path, data_percentage)
                    if success:
                        st.success("✅ Data loaded successfully!")
                        st.session_state.data_loaded = True
                    else:
                        st.error("❌ Data loading failed!")
            else:
                st.warning("⚠️ Dataset not found. Please download first.")

    # Data status
    if st.session_state.data_loaded and st.session_state.features is not None:
        st.markdown("### 📊 Data Summary")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Samples", f"{len(st.session_state.features):,}")

        with col2:
            st.metric("Classes", f"{len(np.unique(st.session_state.labels))}")

        with col3:
            st.metric("Shape", f"{st.session_state.features.shape}")

        with col4:
            memory_mb = st.session_state.features.nbytes / 1024 / 1024
            st.metric("Memory", f"{memory_mb:.0f} MB")

        # Sample data preview
        st.markdown("### 🔍 Data Preview")

        if st.session_state.s2p_map:
            sample_signs = list(st.session_state.s2p_map.keys())[:10]
            df_preview = pd.DataFrame({
                'Sign': sample_signs,
                'Label': [st.session_state.s2p_map[sign] for sign in sample_signs]
            })
            st.dataframe(df_preview, use_container_width=True)

def load_asl_data(data_path, percentage=80):
    """Load ASL dataset from local path"""
    try:
        # Load train.csv
        train_csv_path = os.path.join(data_path, "train.csv")
        if not os.path.exists(train_csv_path):
            st.error(f"❌ train.csv not found in {data_path}")
            return False

        path_train_df = pd.read_csv(train_csv_path)
        path_train_df["path"] = data_path + "/" + path_train_df["path"]

        # Load sign mapping
        mapping_path = os.path.join(data_path, "sign_to_prediction_index_map.json")
        if not os.path.exists(mapping_path):
            st.error(f"❌ sign_to_prediction_index_map.json not found in {data_path}")
            return False

        with open(mapping_path) as f:
            s2p_map = json.load(f)

        p2s_map = {v: k for k, v in s2p_map.items()}
        path_train_df["label"] = path_train_df["sign"].map(s2p_map.get)

        # Store mappings in session state
        st.session_state.s2p_map = s2p_map
        st.session_state.p2s_map = p2s_map

        # Load data with specified percentage
        total_samples = len(path_train_df)
        target_samples = int(total_samples * percentage / 100)

        st.info(f"📊 Loading {percentage}% of dataset ({target_samples:,} samples)")

        # Stratified sampling
        samples_per_class = target_samples // len(s2p_map)
        remainder = target_samples % len(s2p_map)

        sampled_dfs = []
        for i, (_, group) in enumerate(path_train_df.groupby('sign')):
            class_samples = samples_per_class + (1 if i < remainder else 0)
            class_samples = min(class_samples, len(group))

            if class_samples > 0:
                sampled_group = group.sample(n=class_samples, random_state=SEED)
                sampled_dfs.append(sampled_group)

        sampled_df = pd.concat(sampled_dfs, ignore_index=True)
        sampled_df = sampled_df.sample(frac=1, random_state=SEED).reset_index(drop=True)

        # Load features
        features, labels = load_features_parallel(sampled_df)

        if features is not None and labels is not None:
            st.session_state.features = features
            st.session_state.labels = labels
            return True

        return False

    except Exception as e:
        st.error(f"❌ Error loading data: {e}")
        return False

def load_sample_optimized(args):
    """Optimized sample loading function"""
    file_path, label, idx = args
    try:
        df = pd.read_parquet(file_path, columns=['x', 'y', 'z'])
        if len(df) == 0:
            return idx, None, label

        n_frames = len(df) // ROWS_PER_FRAME
        if n_frames == 0:
            return idx, None, label

        # Process data
        data = df.values[:n_frames * ROWS_PER_FRAME].reshape(n_frames, ROWS_PER_FRAME, 3)
        data = np.nan_to_num(data.astype(np.float32), nan=0.0)
        data = data[:, LANDMARK, :]

        # Fixed frame length
        FIXED_FRAME = 22
        if n_frames != FIXED_FRAME:
            if n_frames >= FIXED_FRAME:
                indices = np.linspace(0, n_frames-1, FIXED_FRAME, dtype=int)
                data = data[indices]
            else:
                padded = np.zeros((FIXED_FRAME, len(LANDMARK), N_DATA), dtype=np.float32)
                padded[:n_frames] = data
                if n_frames > 0:
                    padded[n_frames:] = data[-1]
                data = padded

        return idx, data, label
    except Exception:
        return idx, None, label

def load_features_parallel(sampled_df):
    """Load features using parallel processing"""
    try:
        args_list = [(row.path, row.label, idx) for idx, row in sampled_df.iterrows()]

        # Shape configuration
        FIXED_FRAME = 22
        SHAPE = [FIXED_FRAME, len(LANDMARK), N_DATA]

        features = np.zeros([len(sampled_df)] + SHAPE, dtype=np.float32)
        labels = np.zeros(len(sampled_df), dtype=np.int32)

        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        successful = 0

        # Multi-threaded processing
        with ThreadPoolExecutor(max_workers=4) as executor:
            results = list(executor.map(load_sample_optimized, args_list))

        # Process results
        for i, (idx, data, label) in enumerate(results):
            progress_bar.progress((i + 1) / len(results))
            status_text.text(f"Processing sample {i + 1}/{len(results)}")

            if data is not None:
                features[idx] = data
                labels[idx] = label
                successful += 1

        progress_bar.progress(1.0)
        status_text.text(f"✅ Loaded {successful}/{len(results)} samples successfully")

        # Filter valid samples
        if successful < len(sampled_df):
            valid_mask = labels > 0
            features = features[valid_mask]
            labels = labels[valid_mask]

        return features, labels

    except Exception as e:
        st.error(f"❌ Error in parallel loading: {e}")
        return None, None

def show_data_processing():
    """Display data processing page"""
    st.markdown('<h2 class="sub-header">🔧 Data Processing</h2>', unsafe_allow_html=True)

    if not st.session_state.data_loaded:
        st.warning("⚠️ Please load data first.")
        return

    st.markdown("### 📊 Current Data Status")

    if st.session_state.features is not None:
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Samples", f"{len(st.session_state.features):,}")

        with col2:
            st.metric("Shape", f"{st.session_state.features.shape}")

        with col3:
            st.metric("Classes", f"{len(np.unique(st.session_state.labels))}")

        with col4:
            memory_mb = st.session_state.features.nbytes / 1024 / 1024
            st.metric("Memory", f"{memory_mb:.0f} MB")

    # Processing options
    st.markdown("### ⚙️ Processing Options")

    col1, col2 = st.columns(2)

    with col1:
        enable_pyspark = st.checkbox(
            "Enable PySpark Processing",
            value=True,
            help="Use PySpark for advanced feature engineering"
        )

    with col2:
        feature_engineering = st.checkbox(
            "Advanced Feature Engineering",
            value=True,
            help="Apply statistical and temporal feature extraction"
        )

    # Processing controls
    st.markdown("### 🚀 Start Processing")

    if st.button("🔧 Process Data", type="primary"):
        if st.session_state.features is not None:
            with st.spinner("Processing data..."):
                processed_features, processed_labels = process_features(
                    st.session_state.features,
                    st.session_state.labels,
                    enable_pyspark,
                    feature_engineering
                )

                if processed_features is not None:
                    st.session_state.features = processed_features
                    st.session_state.labels = processed_labels
                    st.success("✅ Data processing complete!")
                else:
                    st.error("❌ Data processing failed!")
        else:
            st.warning("⚠️ No data to process!")

def process_features(features, labels, use_pyspark=True, advanced_features=True):
    """Process features with optional PySpark integration"""
    try:
        if use_pyspark:
            st.info("🐘 Using PySpark for feature processing...")
            # Simplified feature processing (PySpark integration would be complex in Streamlit)
            # For demo purposes, we'll use basic processing

        if advanced_features:
            st.info("🔬 Applying advanced feature engineering...")
            # Basic feature engineering
            processed_features = apply_feature_engineering(features)
        else:
            processed_features = features

        return processed_features, labels

    except Exception as e:
        st.error(f"❌ Processing error: {e}")
        return None, None

def apply_feature_engineering(features):
    """Apply basic feature engineering"""
    try:
        # Flatten for processing
        original_shape = features.shape
        flat_features = features.reshape(len(features), -1)

        # Simple feature selection (every 3rd feature)
        selected_indices = list(range(0, flat_features.shape[1], 3))
        enhanced_features = flat_features[:, selected_indices]

        # Reshape back to 3D for CNN
        frames = original_shape[1]
        landmarks = original_shape[2]
        new_features_per_landmark = enhanced_features.shape[1] // (frames * landmarks)

        if new_features_per_landmark > 0:
            new_shape = (len(enhanced_features), frames, landmarks, new_features_per_landmark)
            reshaped_features = enhanced_features[:, :frames*landmarks*new_features_per_landmark]
            processed_features = reshaped_features.reshape(new_shape)
        else:
            processed_features = features

        return processed_features

    except Exception as e:
        st.error(f"❌ Feature engineering error: {e}")
        return features

def show_model_training():
    """Display model training page"""
    st.markdown('<h2 class="sub-header">🤖 Model Training</h2>', unsafe_allow_html=True)

    if not st.session_state.data_loaded or st.session_state.features is None:
        st.warning("⚠️ Please load and process data first.")
        return

    # Model configuration
    st.markdown("### ⚙️ Model Configuration")

    col1, col2, col3 = st.columns(3)

    with col1:
        batch_size = st.selectbox(
            "Batch Size",
            [32, 64, 128, 256],
            index=1,
            help="Training batch size"
        )

    with col2:
        epochs = st.slider(
            "Epochs",
            min_value=5,
            max_value=50,
            value=30,
            help="Number of training epochs"
        )

    with col3:
        validation_split = st.slider(
            "Validation Split",
            min_value=0.1,
            max_value=0.3,
            value=0.2,
            step=0.05,
            help="Fraction of data for validation"
        )

    # Advanced settings
    with st.expander("🔧 Advanced Settings"):
        col1, col2 = st.columns(2)

        with col1:
            learning_rate = st.number_input(
                "Learning Rate",
                min_value=0.0001,
                max_value=0.01,
                value=0.001,
                step=0.0001,
                format="%.4f"
            )

            dropout_rate = st.slider(
                "Dropout Rate",
                min_value=0.1,
                max_value=0.5,
                value=0.2,
                step=0.05
            )

        with col2:
            early_stopping_patience = st.number_input(
                "Early Stopping Patience",
                min_value=3,
                max_value=15,
                value=10,
                step=1
            )

            reduce_lr_patience = st.number_input(
                "Reduce LR Patience",
                min_value=2,
                max_value=10,
                value=4,
                step=1
            )

    # Model architecture preview
    st.markdown("### 🏗️ Model Architecture")

    if st.session_state.features is not None:
        input_shape = st.session_state.features.shape[1:]
        num_classes = len(np.unique(st.session_state.labels))

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Input Shape", f"{input_shape}")

        with col2:
            st.metric("Classes", f"{num_classes}")

        with col3:
            estimated_params = estimate_model_parameters(input_shape, num_classes)
            st.metric("Est. Parameters", f"{estimated_params:,}")

    # Training controls
    st.markdown("### 🚀 Training Controls")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🏗️ Build Model", type="secondary"):
            with st.spinner("Building model..."):
                model = create_optimized_1d_cnn(
                    input_shape,
                    num_classes,
                    dropout_rate,
                    learning_rate
                )

                if model is not None:
                    st.session_state.model = model
                    st.success("✅ Model built successfully!")

                    # Display model summary
                    with st.expander("📊 Model Summary"):
                        model_summary = []
                        model.summary(print_fn=lambda x: model_summary.append(x))
                        st.text('\n'.join(model_summary))
                else:
                    st.error("❌ Model building failed!")

    with col2:
        if st.button("🚀 Start Training", type="primary"):
            if st.session_state.model is not None:
                with st.spinner("Training model..."):
                    history = train_model(
                        st.session_state.model,
                        st.session_state.features,
                        st.session_state.labels,
                        batch_size,
                        epochs,
                        validation_split,
                        early_stopping_patience,
                        reduce_lr_patience
                    )

                    if history is not None:
                        st.session_state.history = history
                        st.session_state.model_trained = True
                        st.success("✅ Training completed!")
                    else:
                        st.error("❌ Training failed!")
            else:
                st.warning("⚠️ Please build model first!")

    with col3:
        if st.button("💾 Save Model"):
            if st.session_state.model is not None:
                model_path = "asl_model.keras"
                try:
                    st.session_state.model.save(model_path)
                    st.success(f"✅ Model saved to {model_path}")
                except Exception as e:
                    st.error(f"❌ Save failed: {e}")
            else:
                st.warning("⚠️ No model to save!")

    # Training status and results
    if st.session_state.model_trained and st.session_state.history is not None:
        st.markdown("### 📊 Training Results")

        history = st.session_state.history.history

        # Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            final_acc = history['accuracy'][-1]
            st.metric("Final Training Acc", f"{final_acc:.1%}")

        with col2:
            final_val_acc = history['val_accuracy'][-1]
            st.metric("Final Validation Acc", f"{final_val_acc:.1%}")

        with col3:
            best_val_acc = max(history['val_accuracy'])
            st.metric("Best Validation Acc", f"{best_val_acc:.1%}")

        with col4:
            final_loss = history['loss'][-1]
            st.metric("Final Loss", f"{final_loss:.4f}")

        # Training curves
        fig = create_training_plots(history)
        st.plotly_chart(fig, use_container_width=True)

def estimate_model_parameters(input_shape, num_classes):
    """Estimate number of model parameters"""
    # Rough estimation for 1D CNN
    conv_params = 64 * 7 * input_shape[-1] + 128 * 5 * 64 + 256 * 3 * 128 + 512 * 3 * 256
    dense_params = 512 * 256 + 256 * 128 + 128 * num_classes
    return conv_params + dense_params

def create_optimized_1d_cnn(input_shape, num_classes, dropout_rate=0.2, learning_rate=0.001):
    """Create optimized 1D CNN model"""
    try:
        inputs = layers.Input(shape=input_shape, name="asl_input")

        # Reshape for 1D CNN
        x = layers.Reshape((input_shape[0], -1), name="reshape_1d")(inputs)

        # Convolutional blocks
        x = layers.Conv1D(64, 7, activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)
        x = layers.MaxPooling1D(2)(x)

        x = layers.Conv1D(128, 5, activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)
        x = layers.MaxPooling1D(2)(x)

        x = layers.Conv1D(256, 3, activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)
        x = layers.MaxPooling1D(2)(x)

        x = layers.Conv1D(512, 3, activation='relu', padding='same')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)

        # Global pooling and dense layers
        x = layers.GlobalAveragePooling1D()(x)
        x = layers.Dense(512, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)

        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(dropout_rate)(x)

        x = layers.Dense(128, activation='relu')(x)
        x = layers.Dropout(dropout_rate)(x)

        outputs = layers.Dense(num_classes, activation='softmax', name="predictions")(x)

        model = tf.keras.Model(inputs, outputs, name="ASL_1D_CNN")

        # Compile model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    except Exception as e:
        st.error(f"❌ Model creation error: {e}")
        return None

def train_model(model, features, labels, batch_size, epochs, validation_split,
                early_stopping_patience, reduce_lr_patience):
    """Train the model with progress tracking"""
    try:
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            features, labels, test_size=validation_split,
            random_state=SEED, stratify=labels
        )

        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor="val_accuracy",
                patience=early_stopping_patience,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor="val_accuracy",
                factor=0.7,
                patience=reduce_lr_patience,
                min_lr=1e-7,
                verbose=1
            )
        ]

        # Create progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        metrics_placeholder = st.empty()

        # Custom callback for Streamlit updates
        class StreamlitCallback(tf.keras.callbacks.Callback):
            def on_epoch_end(self, epoch, logs=None):
                progress = (epoch + 1) / epochs
                progress_bar.progress(progress)
                status_text.text(f"Epoch {epoch + 1}/{epochs}")

                if logs:
                    metrics_placeholder.write({
                        'Epoch': epoch + 1,
                        'Loss': f"{logs.get('loss', 0):.4f}",
                        'Accuracy': f"{logs.get('accuracy', 0):.1%}",
                        'Val Loss': f"{logs.get('val_loss', 0):.4f}",
                        'Val Accuracy': f"{logs.get('val_accuracy', 0):.1%}"
                    })

        callbacks.append(StreamlitCallback())

        # Train model
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=0,
            shuffle=True
        )

        progress_bar.progress(1.0)
        status_text.text("✅ Training completed!")

        return history

    except Exception as e:
        st.error(f"❌ Training error: {e}")
        return None

def create_training_plots(history):
    """Create training progress plots"""
    try:
        epochs = range(1, len(history['accuracy']) + 1)

        # Create subplots
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Model Accuracy', 'Model Loss'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}]]
        )

        # Accuracy plot
        fig.add_trace(
            go.Scatter(
                x=list(epochs),
                y=history['accuracy'],
                mode='lines+markers',
                name='Training Accuracy',
                line=dict(color='blue', width=2),
                marker=dict(size=4)
            ),
            row=1, col=1
        )

        fig.add_trace(
            go.Scatter(
                x=list(epochs),
                y=history['val_accuracy'],
                mode='lines+markers',
                name='Validation Accuracy',
                line=dict(color='red', width=2),
                marker=dict(size=4)
            ),
            row=1, col=1
        )

        # Loss plot
        fig.add_trace(
            go.Scatter(
                x=list(epochs),
                y=history['loss'],
                mode='lines+markers',
                name='Training Loss',
                line=dict(color='blue', width=2),
                marker=dict(size=4),
                showlegend=False
            ),
            row=1, col=2
        )

        fig.add_trace(
            go.Scatter(
                x=list(epochs),
                y=history['val_loss'],
                mode='lines+markers',
                name='Validation Loss',
                line=dict(color='red', width=2),
                marker=dict(size=4),
                showlegend=False
            ),
            row=1, col=2
        )

        # Update layout
        fig.update_xaxes(title_text="Epoch", row=1, col=1)
        fig.update_xaxes(title_text="Epoch", row=1, col=2)
        fig.update_yaxes(title_text="Accuracy", row=1, col=1)
        fig.update_yaxes(title_text="Loss", row=1, col=2)

        fig.update_layout(
            title="Training Progress",
            height=400,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig

    except Exception as e:
        st.error(f"❌ Plot creation error: {e}")
        return None

def show_random_testing():
    """Display random testing page"""
    st.markdown('<h2 class="sub-header">🎯 Random Test Data Selection</h2>', unsafe_allow_html=True)

    if not st.session_state.model_trained or st.session_state.model is None:
        st.warning("⚠️ Please train a model first.")
        return

    # Test configuration
    st.markdown("### ⚙️ Test Configuration")

    col1, col2 = st.columns(2)

    with col1:
        num_samples = st.slider(
            "Number of Test Samples",
            min_value=1,
            max_value=50,
            value=10,
            help="Number of random samples to test"
        )

    with col2:
        confidence_threshold = st.slider(
            "Confidence Threshold",
            min_value=0.1,
            max_value=1.0,
            value=0.5,
            step=0.1,
            help="Minimum confidence for predictions"
        )

    # Generate random test samples
    if st.button("🎲 Generate Random Test", type="primary"):
        if st.session_state.features is not None and st.session_state.labels is not None:
            # Select random samples
            total_samples = len(st.session_state.features)
            random_indices = np.random.choice(total_samples, num_samples, replace=False)

            test_features = st.session_state.features[random_indices]
            test_labels = st.session_state.labels[random_indices]

            # Get predictions
            with st.spinner("Getting predictions..."):
                predictions = st.session_state.model.predict(test_features, verbose=0)
                pred_classes = np.argmax(predictions, axis=1)
                confidence_scores = np.max(predictions, axis=1)

            # Display results
            st.markdown("### 🔍 Test Results")

            # Summary metrics
            accuracy = np.mean(pred_classes == test_labels)
            avg_confidence = np.mean(confidence_scores)
            high_confidence_count = np.sum(confidence_scores >= confidence_threshold)

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Accuracy", f"{accuracy:.1%}")

            with col2:
                st.metric("Avg Confidence", f"{avg_confidence:.3f}")

            with col3:
                st.metric("High Confidence", f"{high_confidence_count}/{num_samples}")

            with col4:
                correct_count = np.sum(pred_classes == test_labels)
                st.metric("Correct", f"{correct_count}/{num_samples}")

            # Detailed results table
            st.markdown("### 📊 Detailed Results")

            results_data = []
            for i in range(num_samples):
                true_sign = st.session_state.p2s_map.get(int(test_labels[i]), f"CLASS_{test_labels[i]}")
                pred_sign = st.session_state.p2s_map.get(int(pred_classes[i]), f"CLASS_{pred_classes[i]}")

                results_data.append({
                    'Sample': i + 1,
                    'True Sign': true_sign.upper(),
                    'Predicted Sign': pred_sign.upper(),
                    'Confidence': f"{confidence_scores[i]:.3f}",
                    'Correct': "✅" if test_labels[i] == pred_classes[i] else "❌",
                    'High Conf': "🔥" if confidence_scores[i] >= confidence_threshold else "❄️"
                })

            results_df = pd.DataFrame(results_data)
            st.dataframe(results_df, use_container_width=True)

            # Confidence distribution
            st.markdown("### 📈 Confidence Distribution")

            fig = px.histogram(
                x=confidence_scores,
                nbins=20,
                title="Prediction Confidence Distribution",
                labels={'x': 'Confidence Score', 'y': 'Count'}
            )
            fig.add_vline(
                x=confidence_threshold,
                line_dash="dash",
                line_color="red",
                annotation_text=f"Threshold: {confidence_threshold}"
            )
            st.plotly_chart(fig, use_container_width=True)

def show_metrics_dashboard():
    """Display comprehensive metrics dashboard"""
    st.markdown('<h2 class="sub-header">📈 Metrics Dashboard</h2>', unsafe_allow_html=True)

    if not st.session_state.model_trained or st.session_state.model is None:
        st.warning("⚠️ Please train a model first.")
        return

    # Overall performance metrics
    st.markdown("### 🎯 Overall Performance")

    if st.session_state.history is not None:
        history = st.session_state.history.history

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            best_val_acc = max(history['val_accuracy'])
            st.metric("Best Validation Accuracy", f"{best_val_acc:.1%}")

        with col2:
            final_val_acc = history['val_accuracy'][-1]
            st.metric("Final Validation Accuracy", f"{final_val_acc:.1%}")

        with col3:
            best_epoch = history['val_accuracy'].index(best_val_acc) + 1
            st.metric("Best Epoch", f"{best_epoch}")

        with col4:
            total_epochs = len(history['val_accuracy'])
            st.metric("Total Epochs", f"{total_epochs}")

    # Training curves
    st.markdown("### 📊 Training Progress")

    if st.session_state.history is not None:
        fig = create_training_plots(st.session_state.history.history)
        if fig:
            st.plotly_chart(fig, use_container_width=True)

    # Model evaluation on test set
    st.markdown("### 🔍 Model Evaluation")

    if st.button("🧪 Evaluate on Test Set", type="primary"):
        if st.session_state.features is not None and st.session_state.labels is not None:
            with st.spinner("Evaluating model..."):
                # Create test set
                test_size = min(2000, len(st.session_state.features))
                test_indices = np.random.choice(len(st.session_state.features), test_size, replace=False)

                test_features = st.session_state.features[test_indices]
                test_labels = st.session_state.labels[test_indices]

                # Get predictions
                predictions = st.session_state.model.predict(test_features, verbose=0)
                pred_classes = np.argmax(predictions, axis=1)
                confidence_scores = np.max(predictions, axis=1)

                # Calculate metrics
                test_accuracy = np.mean(pred_classes == test_labels)
                avg_confidence = np.mean(confidence_scores)

                # Top-5 accuracy
                top5_correct = 0
                for i in range(len(test_labels)):
                    top5_indices = np.argsort(predictions[i])[-5:]
                    if test_labels[i] in top5_indices:
                        top5_correct += 1
                top5_accuracy = top5_correct / len(test_labels)

                # Display results
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Test Accuracy", f"{test_accuracy:.1%}")

                with col2:
                    st.metric("Top-5 Accuracy", f"{top5_accuracy:.1%}")

                with col3:
                    st.metric("Avg Confidence", f"{avg_confidence:.3f}")

                with col4:
                    high_conf_count = np.sum(confidence_scores > 0.8)
                    st.metric("High Confidence (>80%)", f"{high_conf_count}/{test_size}")

                # Confidence distribution
                st.markdown("### 📊 Confidence Analysis")

                fig = create_confidence_analysis(confidence_scores, pred_classes == test_labels)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)

                # Class performance analysis
                st.markdown("### 🎯 Class Performance Analysis")

                class_performance = analyze_class_performance(test_labels, pred_classes, confidence_scores)
                if class_performance:
                    st.dataframe(class_performance, use_container_width=True)

                # Confusion matrix (for top classes)
                st.markdown("### 🔥 Confusion Matrix (Top 20 Classes)")

                fig_cm = create_confusion_matrix(test_labels, pred_classes, top_n=20)
                if fig_cm:
                    st.plotly_chart(fig_cm, use_container_width=True)

def create_confidence_analysis(confidence_scores, correct_predictions):
    """Create confidence analysis visualization"""
    try:
        # Create confidence bins
        bins = np.linspace(0, 1, 11)
        bin_centers = (bins[:-1] + bins[1:]) / 2

        # Calculate accuracy for each confidence bin
        bin_accuracies = []
        bin_counts = []

        for i in range(len(bins) - 1):
            mask = (confidence_scores >= bins[i]) & (confidence_scores < bins[i + 1])
            if np.sum(mask) > 0:
                bin_accuracy = np.mean(correct_predictions[mask])
                bin_count = np.sum(mask)
            else:
                bin_accuracy = 0
                bin_count = 0

            bin_accuracies.append(bin_accuracy)
            bin_counts.append(bin_count)

        # Create subplot
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Confidence vs Accuracy', 'Confidence Distribution'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}]]
        )

        # Confidence vs Accuracy
        fig.add_trace(
            go.Scatter(
                x=bin_centers,
                y=bin_accuracies,
                mode='lines+markers',
                name='Accuracy',
                line=dict(color='blue', width=3),
                marker=dict(size=8)
            ),
            row=1, col=1
        )

        # Perfect calibration line
        fig.add_trace(
            go.Scatter(
                x=[0, 1],
                y=[0, 1],
                mode='lines',
                name='Perfect Calibration',
                line=dict(color='red', dash='dash'),
                showlegend=False
            ),
            row=1, col=1
        )

        # Confidence distribution
        fig.add_trace(
            go.Histogram(
                x=confidence_scores,
                nbinsx=20,
                name='Confidence Distribution',
                showlegend=False
            ),
            row=1, col=2
        )

        # Update layout
        fig.update_xaxes(title_text="Confidence Score", row=1, col=1)
        fig.update_xaxes(title_text="Confidence Score", row=1, col=2)
        fig.update_yaxes(title_text="Accuracy", row=1, col=1)
        fig.update_yaxes(title_text="Count", row=1, col=2)

        fig.update_layout(
            title="Model Confidence Analysis",
            height=400,
            showlegend=True
        )

        return fig

    except Exception as e:
        st.error(f"❌ Confidence analysis error: {e}")
        return None

def analyze_class_performance(true_labels, pred_labels, confidence_scores):
    """Analyze performance by class"""
    try:
        unique_classes = np.unique(true_labels)
        class_data = []

        for class_label in unique_classes[:30]:  # Top 30 classes
            class_mask = true_labels == class_label
            class_count = np.sum(class_mask)

            if class_count > 0:
                class_accuracy = np.mean(pred_labels[class_mask] == true_labels[class_mask])
                class_avg_confidence = np.mean(confidence_scores[class_mask])

                try:
                    class_name = st.session_state.p2s_map.get(int(class_label), f"CLASS_{class_label}")
                except:
                    class_name = f"CLASS_{class_label}"

                class_data.append({
                    'Class': class_name.upper(),
                    'Label': int(class_label),
                    'Samples': class_count,
                    'Accuracy': f"{class_accuracy:.1%}",
                    'Avg Confidence': f"{class_avg_confidence:.3f}",
                    'Performance': "🔥" if class_accuracy > 0.7 else "👍" if class_accuracy > 0.5 else "📈"
                })

        # Sort by accuracy
        class_data.sort(key=lambda x: float(x['Accuracy'].strip('%')), reverse=True)

        return pd.DataFrame(class_data)

    except Exception as e:
        st.error(f"❌ Class analysis error: {e}")
        return None

def create_confusion_matrix(true_labels, pred_labels, top_n=20):
    """Create confusion matrix for top N classes"""
    try:
        # Get top N most frequent classes
        unique_classes, counts = np.unique(true_labels, return_counts=True)
        top_classes_idx = np.argsort(counts)[-top_n:]
        top_classes = unique_classes[top_classes_idx]

        # Filter data for top classes
        mask = np.isin(true_labels, top_classes)
        filtered_true = true_labels[mask]
        filtered_pred = pred_labels[mask]

        # Create confusion matrix
        cm = confusion_matrix(filtered_true, filtered_pred, labels=top_classes)

        # Get class names
        class_names = []
        for class_label in top_classes:
            try:
                class_name = st.session_state.p2s_map.get(int(class_label), f"C{class_label}")
                class_names.append(class_name[:8])  # Truncate for display
            except:
                class_names.append(f"C{class_label}")

        # Create heatmap
        fig = px.imshow(
            cm,
            x=class_names,
            y=class_names,
            color_continuous_scale='Blues',
            title=f"Confusion Matrix (Top {top_n} Classes)",
            labels=dict(x="Predicted", y="True", color="Count")
        )

        fig.update_layout(
            height=600,
            xaxis_title="Predicted Class",
            yaxis_title="True Class"
        )

        return fig

    except Exception as e:
        st.error(f"❌ Confusion matrix error: {e}")
        return None

def show_settings():
    """Display settings page"""
    st.markdown('<h2 class="sub-header">⚙️ Settings</h2>', unsafe_allow_html=True)

    # Model settings
    st.markdown("### 🤖 Model Settings")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 Export Model"):
            if st.session_state.model is not None:
                try:
                    model_path = "asl_model_export.keras"
                    st.session_state.model.save(model_path)

                    with open(model_path, "rb") as f:
                        st.download_button(
                            label="📥 Download Model",
                            data=f.read(),
                            file_name=model_path,
                            mime="application/octet-stream"
                        )

                    st.success("✅ Model ready for download!")
                except Exception as e:
                    st.error(f"❌ Export failed: {e}")
            else:
                st.warning("⚠️ No model to export!")

    with col2:
        uploaded_model = st.file_uploader(
            "📤 Import Model",
            type=['keras', 'h5'],
            help="Upload a trained model file"
        )

        if uploaded_model is not None:
            try:
                # Save uploaded file temporarily
                with open("temp_model.keras", "wb") as f:
                    f.write(uploaded_model.read())

                # Load model
                imported_model = tf.keras.models.load_model("temp_model.keras")
                st.session_state.model = imported_model
                st.session_state.model_trained = True

                # Clean up
                os.remove("temp_model.keras")

                st.success("✅ Model imported successfully!")
            except Exception as e:
                st.error(f"❌ Import failed: {e}")

    # Data settings
    st.markdown("### 📊 Data Settings")

    if st.button("🗑️ Clear All Data"):
        if st.button("⚠️ Confirm Clear All", type="secondary"):
            # Reset session state
            for key in list(st.session_state.keys()):
                if key not in ['kaggle_username', 'kaggle_api_key']:
                    del st.session_state[key]

            # Reinitialize
            st.session_state.data_loaded = False
            st.session_state.model_trained = False
            st.session_state.features = None
            st.session_state.labels = None
            st.session_state.model = None
            st.session_state.history = None

            st.success("✅ All data cleared!")
            st.experimental_rerun()

    # System info
    st.markdown("### 💻 System Information")

    col1, col2 = st.columns(2)

    with col1:
        st.info(f"**TensorFlow Version:** {tf.__version__}")
        st.info(f"**NumPy Version:** {np.__version__}")
        st.info(f"**Pandas Version:** {pd.__version__}")

    with col2:
        if st.session_state.features is not None:
            memory_usage = st.session_state.features.nbytes / 1024 / 1024
            st.info(f"**Memory Usage:** {memory_usage:.0f} MB")

        st.info(f"**Random Seed:** {SEED}")
        st.info(f"**Landmarks:** {len(LANDMARK)}")

if __name__ == "__main__":
    main()
